import { computed, onMounted, ref } from "vue";
import {
  BartenderPrintPayload,
  CommonPrintPayload,
  PrinterItem,
} from "@/api/interface";
import { fetchPrinterList, printTagByBartender, printTagByCommon } from "@/api";
import {
  ConfigProviderProps,
  createDiscreteApi,
  darkTheme,
  lightTheme,
} from "naive-ui";

const themeRef = ref<"light" | "dark">("light");
const configProviderPropsRef = computed<ConfigProviderProps>(() => ({
  theme: themeRef.value === "light" ? lightTheme : darkTheme,
}));

const { message } = createDiscreteApi(["message"], {
  configProviderProps: configProviderPropsRef,
});

export enum PrintType {
  COMMON = "common",
  BARTENDER = "bartender",
}

export const usePrintTags = (printType: PrintType) => {
  const printerList = ref<PrinterItem[]>([]);
  const printFnMap = {
    [PrintType.COMMON]: printTagByCommon,
    [PrintType.BARTENDER]: printTagByBartender,
  };

  onMounted(() => {
    getPrintList();
  });

  const getPrintList = async () => {
    try {
      const res = await fetchPrinterList();
      printerList.value = res.data;
    } catch (e) {
      message.error("请打开打印插件");
      console.error(String(e))
    }
  };

  const printTags = async ({
    printerName,
    ...params
  }: CommonPrintPayload | BartenderPrintPayload) => {
    try {
      await getPrintList();
      if (!printerList.value.some((item) => item.name === printerName)) {
        message.error(`找不到打印机${printerName}`);
        return;
      }
      const res = await printFnMap[printType]({ printerName, ...params });
      if (res.data.code === 201) {
        message.success("正在打印");
      } else {
        message.error(res.data.msg);
      }
    } catch (e) {
      message.error("打印错误，请检查打印机连接");
      console.error(String(e))
    }
  };

  return {
    printTags,
    getPrintList,
    printerList,
  };
};
