{"$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json", "appId": "zw_driver_tool", "asar": true, "productName": "zw_driver_tool", "directories": {"output": "release/"}, "files": ["dist", "dist-electron"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "artifactName": "${productName}_v${version}-test_Setup.${ext}", "icon": "./public/icon.ico"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false}, "publish": [{"provider": "generic", "url": "http://10.1.160.34:7860/${productName}_test"}]}