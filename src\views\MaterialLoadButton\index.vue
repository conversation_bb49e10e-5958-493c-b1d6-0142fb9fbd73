<script setup lang="ts">
import { fetchMaterialLoadRecord, materialLoad, materialUnoad } from '@/api';
import { LoadRecord, OrderItem } from '@/api/interface';
import { useLineStore } from '@/store/modules/line';
import { useUserStore } from '@/store/modules/user';
import { useDialog, useMessage } from 'naive-ui';
import { ref } from 'vue';
import { VxeGridProps } from 'vxe-table';

const modalVisible = ref(false)
const lineStore = useLineStore()
const tableData = ref<OrderItem[]>([])
const getTableData = async () => {
  const res = await fetchMaterialLoadRecord(lineStore.lineTaskInfo.order_no)
  tableData.value = res.data
}
const handleClick = async () => {
  getTableData()
  modalVisible.value = true
}
const gridConfig: VxeGridProps<OrderItem> = {
  columns: [
    { type: 'expand', width: 40, slots: { content: 'content' } },
    { field: "order_no", title: "工单号" },
    { field: "material_code", title: "物料编码" },
    { field: "material_name", title: "物料名称" },
    { field: "material_unit", title: "物料单位" },
    { field: "demand_qty", title: "需求数量" },
    { field: "load_qty", title: "上料数量" },
  ],
  radioConfig: {
    highlight: true,
  },
  border: true,
  height: '100%'
};
const contentConfig: VxeGridProps<LoadRecord> = {
  columns: [
    { type: 'checkbox', width: 40 },
    { field: 'material_code', title: '物料编码' },
    { field: 'serial_number', title: '条码' },
    { field: 'balance_qty', title: '数量' },
    { field: 'username', title: '上料人' },
    { field: 'creation_date', title: '上料时间' },
    { title: '操作', slots: { default: 'operation' } },
  ],
  border: true,
}
const serial_number = ref('')
const userStore = useUserStore()
const message = useMessage()
const handleScan = async () => {
  try {
    const res = await materialLoad({ workcode: userStore.checker.workcode, line_code: lineStore.lineInfo.line_code, task_no: lineStore.lineTaskInfo.task_no, serial_number: serial_number.value })
    message.success(res.msg)
    getTableData()
  } finally {
    serial_number.value = ''
  }
}
const dialog = useDialog()
const handleUnload = (row: LoadRecord, rowIndex: number) => {
  dialog.warning({
    title: '提示',
    content: '是否确认卸料？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const res = await materialUnoad({ workcode: userStore.checker.workcode, line_code: lineStore.lineInfo.line_code, task_no: lineStore.lineTaskInfo.task_no, serial_number: row.serial_number })
      message.success(res.msg)
      await getTableData()
      tableData.value[rowIndex].load_records = tableData.value[rowIndex].load_records.filter(item => item.serial_number !== row.serial_number)
    }
  })
}
</script>

<template>
  <n-button class="w-full h-16 bg-[#1867c0] text-white" size="large" @click="handleClick">上料</n-button>

  <n-modal v-model:show="modalVisible" preset="card" title="上料" class="w-screen h-screen">
    <div class="w-full h-full">
      <div class="flex mb-4 justify-center items-center">
        <div class="w-16 text-center">条码</div>
        <n-input placeholder="请扫描" @keyup.enter="handleScan" v-model:value="serial_number"></n-input>
      </div>
      <div class="w-full h-[calc(100%-50px)]">
        <vxe-grid v-bind="gridConfig" :data="tableData">
          <template #content="{ row, rowIndex }">
            <div class="p-8">
              <vxe-grid v-bind="contentConfig" :data="row.load_records">
                <template #operation="{ row }">
                  <n-button type="error" size="mini" @click="handleUnload(row, rowIndex)">卸料</n-button>
                </template>
              </vxe-grid>
            </div>
          </template>
        </vxe-grid>
      </div>
    </div>
  </n-modal>
</template>