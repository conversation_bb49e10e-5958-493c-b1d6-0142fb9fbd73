<script setup lang="ts">
import { startLineTask } from '@/api';
import { useLineStore } from '@/store/modules/line';
import { useUserStore } from '@/store/modules/user';
import { useMessage } from 'naive-ui';

const lineStore = useLineStore()
const userStore = useUserStore()
const message = useMessage()
const handleClick = async () => {
  const res = await startLineTask({ line_code: lineStore.lineInfo.line_code, task_no: lineStore.lineTaskInfo.task_no, workcode: userStore.checker.workcode })
  message.success(res.msg)
  lineStore.getLineTaskInfo()
}
</script>

<template>
  <!-- 生产中禁止点击 -->
  <n-button class="w-full h-16 bg-[#1867c0] text-white" size="large" @click="handleClick"
    :disabled="lineStore.lineTaskInfo.task_status === 'started'">工单开始</n-button>
</template>