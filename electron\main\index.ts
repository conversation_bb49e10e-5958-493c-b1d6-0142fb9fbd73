import { app, BrowserWindow, dialog, ipcMain, shell } from "electron";
import { createRequire } from "node:module";
import { fileURLToPath } from "node:url";
import path from "node:path";
import os from "node:os";
import electronUpdater from "electron-updater";
import * as http from "node:http";
import printer from "@thiagoelg/node-printer";
import bodyParser from "body-parser";
import express from "express";

const formatMac = (mac: string) => {
  if (!mac) return "";
  const cleaned = mac.replace(/[^0-9a-fA-F]/g, "");
  if (cleaned.length !== 12) {
    return "";
  }
  return cleaned
    .match(/.{1,2}/g)
    ?.join("-")
};

// electron Security Warning (Insecure Content-Security-Policy)
process.env["ELECTRON_DISABLE_SECURITY_WARNINGS"] = "true";

const require = createRequire(import.meta.url);
const __dirname = path.dirname(fileURLToPath(import.meta.url));

process.env.APP_ROOT = path.join(__dirname, "../..");

const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;
const VITE_WEBVIEW_URL = import.meta.env.VITE_WEBVIEW_URL;

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL
  ? path.join(process.env.APP_ROOT, "public")
  : RENDERER_DIST;

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL
  ? path.join(process.env.APP_ROOT, "public")
  : RENDERER_DIST;

// win7关闭硬件加速
if (os.release().startsWith("6.1")) app.disableHardwareAcceleration();

if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

let win: BrowserWindow | null = null;
const preload = path.join(__dirname, "../preload/index.cjs");
const indexHtml = path.join(RENDERER_DIST, "index.html");

const createWindow = async () => {
  win = new BrowserWindow({
    title: "苏州驱动",
    width: 600,
    height: 600,
    icon: path.join(process.env.VITE_PUBLIC, "icon.ico"),
    webPreferences: {
      preload,
    },
  });

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else if (VITE_WEBVIEW_URL) {
    win.loadURL(VITE_WEBVIEW_URL);
  } else {
    win.loadFile(indexHtml);
  }

  win.setMenu(null);
  win.setFullScreen(true);

  win.webContents.on("did-finish-load", () => {
    win.webContents.send("main-process-message", new Date().toLocaleString());
  });

  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith("https:")) shell.openExternal(url);
    return { action: "deny" };
  });

  const log = console.log;
  global.console.log = (...args) => {
    win.webContents.send("console-log", ...args.map((e) => e.toString()));
    log(...args);
  };

  ipcMain.on("check-update", () => {
    console.log("check-update");
    autoUpdater.checkForUpdates();
  });

  ipcMain.on("open-dev-tools", () => {
    win.webContents.toggleDevTools();
  });

  ipcMain.on("get-mac", () => {
    const network = os.networkInterfaces();
    const fields = ["WLAN", "以太", "连接"];
    let mac = "";
    if (network) {
      const keys = Object.keys(network);
      const field = keys.find((o) => fields.some((i) => o.includes(i)));
      if (field && network[field][0]) {
        mac = network[field][0].mac;
      }
    }
    win.webContents.send("set-mac", formatMac(mac));
  });

  ipcMain.on("mini", () => {
    win.minimize();
  });
};

app.whenReady().then(async () => {
  await createWindow();
  checkUpdate();
  createPrinterServer()
});

app.on("window-all-closed", () => {
  win = null;
  if (process.platform !== "darwin") app.quit();
});

const { autoUpdater } = electronUpdater;
autoUpdater.setFeedURL(`http://***********:7860/zw_driver_tool`);
// 是否自动下载
autoUpdater.autoDownload = false;
const checkUpdate = () => {
  autoUpdater.on("update-downloaded", async () => {
    const res = await dialog.showMessageBox({
      title: "应用更新",
      message: "最新版本已下载完成，退出程序进行安装",
      buttons: ["确定"],
    });
    if (res.response === 0) {
      autoUpdater.quitAndInstall();
      app.quit();
    }
  });
  // 有新版本
  autoUpdater.on("update-available", () => {
    console.log("update-available");
    win.webContents.send("update-available");
  });
  // 没有新版本
  autoUpdater.on("update-not-available", () => {
    console.log("update-not-available");
    win.webContents.send("update-not-available");
  });
  autoUpdater.on("error", (error) => {
    console.log("update-error");
    win.webContents.send("update-error", error.message);
  });
  // 手动下载
  ipcMain.on("download-update", () => {
    console.log("download-update");
    autoUpdater.downloadUpdate();
  });
  // 下载进度
  autoUpdater.on("download-progress", (e) => {
    console.log(`download-progress: ${e.percent}`);
  });
};

const createPrinterServer = () => {
  const app = express();
  const printerList = [];
  
  app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", req.headers.origin || "*");
    res.header("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS");
    res.header("Access-Control-Allow-Headers", "Authorization,Content-Type");
    res.header("Access-Control-Allow-Credentials", "true");
    if (req.method === "OPTIONS") {
      res.sendStatus(200);
    } else {
      next();
    }
  });
  
  app.use(bodyParser.json());
  
  app.get("/print", (req, res) => {
    if (req.query.noCache) {
      res.json(printer.getPrinters());
    } else {
      if (printerList.length) {
        res.json(printerList);
      } else {
        const list = printer.getPrinters();
        printerList.push(...list);
        res.json(printerList);
      }
    }
  });
  
  app.post("/print", (req, res) => {
    const { printerName, rowCode } = req.body;
    if (!printerName || !rowCode) {
      res.json({ code: 400, msg: "缺少{printerName}或{rawCode}参数" });
      return;
    }
    
    printer.printDirect({
      data: rowCode,
      printer: printerName,
      type: "RAW",
      success: () => res.json({ code: 201, msg: printerName }),
      error: (e) => res.json({ code: 500, msg: e }),
    });
  });
  
  http.createServer(app).listen(3001, () => {
    console.log("printer server listening on http://localhost:3001");
  });
}
