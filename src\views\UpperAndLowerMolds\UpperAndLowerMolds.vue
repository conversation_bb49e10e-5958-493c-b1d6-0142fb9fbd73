<template>
  <n-modal v-model:show="dialog" preset="card" title="上下模" style="width: 1000px">
    <n-card>
      <n-layout>
        <n-grid :cols="2" :x-gap="13">
          <n-gi>
            <n-space vertical>
              <n-space style="display: flex">
                <n-select
                  style="width: 100px"
                  :options="items"
                  label-field="label"
                  value-field="value"
                  v-model:value="preVal"
                  placeholder="前缀"
                />
                <n-input v-model:value="modVal" placeholder="输入模具号" />
                <span style="line-height: 30px">款别</span>
                <n-select
                  style="width: 100px"
                  :options="specCodeItems"
                  label-field="spec_code"
                  value-field="spec_code"
                  v-model:value="spec_code"
                  placeholder="款别"
                />
              </n-space>
              <NumKeyboard v-model:value="modVal" @change="val=>{
                modVal = val}" @submit="onLine" @reset="modVal = ''; spec_code = ''" :show-button="!modelData.mold_code"/>
            </n-space>
          </n-gi>
          <n-gi>
            <n-space vertical v-if="modelData && modelData.mold_code">
              类型
              <n-input
                v-model:value="modelData.mold_type_name"
                readonly
                placeholder="类型"
              />
              模具别名
              <n-input
                v-model:value="modelData.mold_alias"
                readonly
                placeholder="模具别名"
              />
              <div style="display: flex; justify-content: center">
                <n-button type="primary" @click="offline">下线</n-button>
              </div>
            </n-space>
            <div v-else style="color: #999; padding: 30px">无</div>
          </n-gi>
        </n-grid>
      </n-layout>
      <template #footer>
        <n-space justify="end">
          <n-button @click="hide">关闭</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import NumKeyboard from "@/components/NumKeyboard";
import { useMessage } from 'naive-ui'
import request from "@/lib/http-client";
// const props = defineProps(["modelData"])
const emit = defineEmits(["input", "change"])
const message = useMessage()

const dialog = ref(false)
const lineCode = ref("")
const workCode = ref("")
const modelData = ref({})
const show = (line_code, workcode) => {
  modelData.value = {}
  lineCode.value = line_code
  workCode.value = workcode
  getModelData(line_code)
  dialog.value = true
}
const getModelData = async(line_code) => {
  const res = await request.get(
    import.meta.env.VITE_BASE_URL + "/ims/tablet/mold/online",
    {
      line_code
    },
    { headers: { "Content-Type": "application/json" } }
  )
  modelData.value = res.data || {}
}
const spec_code = ref("")
const modVal = ref("")
const preVal = ref("ZW-")
const items = ref([
  {
    label: "无",
    value: "",
  },
  {
    label: "ZW-",
    value: "ZW-",
  },
  {
    label: "SZW-",
    value: "SZW-",
  },
  {
    label: "ZWPM-",
    value: "ZWPM-",
  },
])
const specCodeItems = ref([])

const onLine = async () => {
  const res = await request.post(
    import.meta.env.VITE_BASE_URL + "/ims/tablet/mold/upline",
    {
      workcode: workCode.value,
      line_code: lineCode.value,
      mold_model: spec_code.value,
      mold_alias: preVal.value + modVal.value
    },
    { headers: { "Content-Type": "application/json" } }
  )

  message.success("上线成功")
  emit("change")
  hide()
}

const moldScraperOffline = async () => {
  const data = {
    workcode: workCode.value,
    line_code: lineCode.value,
    mold_alias: modelData.value.mold_alias
  }
  await request.post(
    import.meta.env.VITE_BASE_URL + "/ims/tablet/mold/offline",
    data,
    { headers: { "Content-Type": "application/json" } }
  )
}

const offline = async () => {
  await moldScraperOffline()
  message.success("下线成功")
  emit("change")
  hide()
}
const hide = () => {
  dialog.value = false
}
const changeModVal = async () => {
  const res = await request.get(
    import.meta.env.VITE_BASE_URL + "/ims/tablet/mold-model",
    { mold_alias: preVal.value + modVal.value }
  )
  specCodeItems.value = res.data.map(i=>{
    return {
      spec_code: i.mold_model
    }
  })

  // 默认选择第一款
  if (specCodeItems.value.length > 0) {
    spec_code.value = specCodeItems.value[0].spec_code
  }
}

watch(modVal, () => {
  changeModVal()
})
defineExpose({
  show
})
</script>
<style scoped>
.n-input__input{
  color: black;
}
</style>
