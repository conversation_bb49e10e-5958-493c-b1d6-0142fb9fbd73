<script setup lang="ts">
import { fetchLineTaskList, selectLineTask } from "@/api";
import { useLineStore } from "@/store/modules/line";
import { ref } from "vue";

const emits = defineEmits(["close"]);
const modalVisible = ref(false);
const lineStore = useLineStore();
const gridRef = ref();
const gridConfig = {
  columns: [
    { type: "radio", width: 40 },
    { field: "task_no", title: "任务号" },
    { field: "order_no", title: "工单号" },
    { field: "product_code", title: "产品编码" },
    { field: "old_product_code", title: "旧产品编码" },
    { field: "product_name", title: "产品名称" },
    { field: "order_qty", title: "工单数量" },
    { field: "produced_qty", title: "已打印数量" },
    { field: "task_qty", title: "任务数量" },
    { field: "plan_start_time", title: "计划开工日期" },
    { field: "task_status", title: "任务状态", slots: { default: 'task_status' } },
  ],
  radioConfig: {
    highlight: true,
  },
  height: "500px",
  border: true,
};
const selected = ref<object | null>(null);
const handleSelect = ({ row }) => {
  selected.value = row;
  gridRef.value.setRadioRow(row);
};
const hide = () => {
  modalVisible.value = false;
  selected.value = null;
  gridRef.value.clearRadioRow();
  emits("close");
};
const confirm = async () => {
  await selectLineTask(lineStore.lineInfo.line_code, selected.value.task_no)
  lineStore.getLineTaskInfo()
  hide();
};
const tableData = ref();
const show = async () => {
  const res = await fetchLineTaskList(lineStore.lineInfo.line_code)
  tableData.value = res.data
  modalVisible.value = true;
};
const taskStatusMap = {
  not_started: '未开始',
  started: '进行中',
  finished: '已完成'
}
</script>

<template>
  <!-- 生产中禁止点击 -->
  <n-button class="w-full h-16 bg-[#607d8b] text-white" size="large" @click="show"
    :disabled="lineStore.lineTaskInfo.task_status === 'started'">选择工单</n-button>

  <n-modal v-model:show="modalVisible" preset="card" title="选择工单" class="w-[80%]">
    <vxe-grid v-bind="gridConfig" :data="tableData" @cell-click="handleSelect" @radio-change="handleSelect"
      ref="gridRef">
      <template #task_status="{ row }">
        <span>{{ taskStatusMap[row.task_status] }}</span>
      </template>
    </vxe-grid>

    <template #footer>
      <n-flex justify="end">
        <n-button @click="hide">取消</n-button>
        <n-button type="info" @click="confirm" :disabled="!selected">确定</n-button>
      </n-flex>
    </template>
  </n-modal>
</template>
