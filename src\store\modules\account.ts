import { defineStore } from "pinia";
import piniaPersistConfig from "@/store/persist";

export const useAccountStore = defineStore("account", {
  state: () => ({
    operator: "", // 作业员
    technician: "", // 技术员
    inspector: "", // 巡检员
    checker: "",
  }),
  actions: {
    setOperator(val) {
      this.operator = val;
    },
    setTechnician(val) {
      this.technician = val;
    },
    setInspector(val) {
      this.inspector = val;
    },
    setChecker(val) {
      this.checker = val;
    },
  },
  persist: piniaPersistConfig("account"),
});
