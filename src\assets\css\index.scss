@import "reset.css";
@import "tailwind.css";

.main-box {
  border: 1px solid theme('colors.border-color');
  border-radius: 4px;
  margin-bottom: 10px;
  .main-title {
    width: 100%;
    padding: 10px;
    border-bottom: 1px solid theme('colors.border-color');
    display: flex;
    align-items: center;
    .uil {
      margin-right: 6px;
      &:before {
        font-size: 20px;
      }
    }
    > span {
      font-weight: bold;
    }
  }
}

.main-box + .main-box {
  margin-left: 10px;
}

.console-btn {
  @apply w-full mb-[10px] text-[theme(colors.white)];
}

.dot-title {
  margin: 10px 0;
  position: relative;
  z-index: 1;
  padding-left: 6px;
  font-weight: bold;
  font-size: 14px;
  color: theme('colors.text-color');
  background: theme('colors.white');
  line-height: 20px;
  display: flex;
  align-items: center;

  &::before {
    position: relative;
    left: -5px;
    display: inline-block;
    content: " ";
    width: 4px;
    height: 14px;
    background: theme('colors.primary-color');
    border-radius: 2px;
  }
}
