import fs from "node:fs";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import electron from "vite-plugin-electron/simple";
import pkg from "./package.json";
import tailwindcss from "tailwindcss";
import * as PostCSS from "postcss";
import autoprefixer from "autoprefixer";
import { resolve } from "node:path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { NaiveUiResolver } from "unplugin-vue-components/resolvers";

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  fs.rmSync("dist-electron", { recursive: true, force: true });

  const isServe = command === "serve";
  const isBuild = command === "build";

  return {
    base: "./",
    assetsInclude: resolve(__dirname, "./src/assets"),
    resolve: {
      alias: {
        "@": resolve(__dirname, "src"),
      },
      extensions: [".js", ".ts", ".jsx", ".tsx", ".vue"],
    },
    css: {
      postcss: {
        plugins: [
          tailwindcss as PostCSS.AcceptedPlugin,
          autoprefixer as PostCSS.AcceptedPlugin,
        ],
      },
    },
    plugins: [
      vue(),
      electron({
        main: {
          entry: "electron/main/index.ts",
          vite: {
            build: {
              sourcemap: isServe,
              minify: isBuild,
              outDir: "dist-electron/main",
              rollupOptions: {
                external: Object.keys(
                  "dependencies" in pkg ? pkg.dependencies : {}
                ),
              },
            },
          },
        },
        preload: {
          input: "electron/preload/index.ts",
          vite: {
            build: {
              sourcemap: isServe ? "inline" : undefined,
              minify: isBuild,
              outDir: "dist-electron/preload",
              rollupOptions: {
                output: {
                  format: "cjs",
                  entryFileNames: "[name].cjs",
                },
                external: [
                  "electron",
                  ...Object.keys("dependencies" in pkg ? pkg.dependencies : {}),
                ],
              },
            },
          },
        },
      }),
      AutoImport({
        resolvers: [NaiveUiResolver()],
      }),
      Components({
        resolvers: [NaiveUiResolver()],
      }),
    ],
    clearScreen: false,
    build: {
      outDir: "dist",
      sourcemap: isServe,
      // 规定触发警告的chunk大小
      chunkSizeWarningLimit: 2048,
      rollupOptions: {
        output: {
          chunkFileNames: "assets/js/[name]-[hash].js", // 引入文件名的名称
          entryFileNames: "assets/js/[name]-[hash].js", // 包的入口文件名称
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]", // 资源文件
        },
      },
    },
  };
});
