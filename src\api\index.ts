import request from "@/lib/http-client";
import {
  CheckRolePermissionPayload,
  CheckRolePermissionResult,
  LineInfo,
  OfflineRolePayload,
  ChangeLineTaskStatusPayload,
  UplineRolePayload,
  UserInfo,
  OrderItem,
  UpdateMaterialStatusPayload,
  InspectionInfo,
  ScanInspectionPayload,
  PrintItem,
  SerialNumberTemplateInfo,
  createSerialNumberPayload,
  OrderPrintInfo,
  PrinterItem,
  BartenderPrintPayload,
  PrintResult,
  CommonPrintPayload,
  PrintContent,
  SubmitInspectionPayload,
  ScrapSerialNumberPayload,
} from "@/api/interface";
import axios, { AxiosResponse } from "axios";

// 根据物理地址获取线别
export const fetchLineInfo = (mac_address: string) => {
  return request.get<LineInfo>("/ims/tablet/line", { mac_address });
};

export const fetchLineTaskList = (line_code: string) => {
  return request.get("/ims/tablet/line-task", { line_code });
};

export const fetchLineTaskInfo = (line_code: string) => {
  return request.get("/ims/tablet/line-task/now", { line_code });
};

export const fetchOnlineRole = (line_code: string) => {
  return request.get<UserInfo[]>("/ims/tablet/role/online", { line_code });
};

export const offlineRole = (data: OfflineRolePayload) => {
  return request.post("/ims/tablet/role/offline", data);
};

export const uplineRole = (data: UplineRolePayload) => {
  return request.post("/ims/tablet/role/upline", data);
};

export const checkRolePermission = (data: CheckRolePermissionPayload) => {
  return request.post<CheckRolePermissionResult>(
    "/ims/tablet/role/check",
    data
  );
};

export const selectLineTask = (line_code: string, task_no: string) => {
  return request.post(
    "/ims/tablet/line-task/select",
    {line_code, task_no}
  );
};

export const startLineTask = (data: ChangeLineTaskStatusPayload) => {
  return request.post("/ims/tablet/line-task/start", data);
};

export const pauseLineTask = (data: ChangeLineTaskStatusPayload) => {
  return request.post("/ims/tablet/line-task/pause", data);
};

export const finishLineTask = (data: ChangeLineTaskStatusPayload) => {
  return request.post("/ims/tablet/line-task/finish", data);
};

export const fetchMaterialLoadRecord = (order_no: string) => {
  return request.get<OrderItem[]>("/ims/tablet/material-load-record", {
    order_no,
  });
};

export const materialLoad = (data: UpdateMaterialStatusPayload) => {
  return request.post("/ims/tablet/material-load", data);
};

export const materialUnoad = (data: UpdateMaterialStatusPayload) => {
  return request.post("/ims/tablet/material-unload", data);
};

export const fetchInspectionRecord = (line_code: string) => {
  return request.get<InspectionInfo>("/ims/tablet/inspection", { line_code });
};

export const scanInspection = (data: ScanInspectionPayload) => {
  return request.post("/ims/tablet/inspection/scan", data);
};

export const submitInspection = (data: SubmitInspectionPayload) => {
  return request.post("/ims/tablet/inspection/submit", data);
};

export const fetchPrintRecord = (order_no: string) => {
  return request.get<OrderPrintInfo>("/ims/tablet/order/serial-number-record", {
    order_no,
  });
};

export const createSerialNumber = (data: createSerialNumberPayload) => {
  return request.post<{ serial_number: string }[]>(
    "/ims/tablet/order/serial-number",
    data
  );
};

export const fetchPrintContent = (serial_number: string) => {
  return request.post<PrintContent>("/ims/serial/sn/print-content", {
    serial_number,
  });
};

export const fetchPrinterList = (): Promise<AxiosResponse<PrinterItem[]>> => {
  return axios.get("http://localhost:3001/print");
};

export const printTagByBartender = (
  data: BartenderPrintPayload
): Promise<AxiosResponse<PrintResult>> => {
  return axios.post("http://localhost:3001/v1/barPrint", data);
};

export const printTagByCommon = (
  data: CommonPrintPayload
): Promise<AxiosResponse<PrintResult>> => {
  return axios.post("http://localhost:3001/print", data);
};

export const fetchMaxPackQty = (product_code: string) => {
  return request.get<{ max_pack_qty: number }>(
    "/ims/tablet/order/max-pack-qty",
    { product_code }
  );
};

export const fetchSerialNumberInfo = (serial_number: string) => {
  return request.get<PrintItem[]>("/ims/tablet/order/serial-number-info", { serial_number });
};

export const fetchSerialNumberTemplateInfo = (serial_number: string) => {
  return request.get<SerialNumberTemplateInfo[]>("/ims/tablet/order/serial-number-template", { serial_number });
};


export const scrapSerialNumber = (data: ScrapSerialNumberPayload) => {
  return request.post("/ims/tablet/order/serial-number-scrap", data);
};

export const removeSerialNumber = (data: {
  workcode: string;
  serial_number: string
  line_code: string
}) => {
  return request.post("/ims/tablet/inspection/remove", data);
};
