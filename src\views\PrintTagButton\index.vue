<script setup lang="ts">
import { fetchPrintRecord, createSerialNumber, fetchPrintContent, fetchMaxPackQty } from '@/api';
import { OrderPrintInfo } from '@/api/interface';
import { PrintItem } from '@/api/interface';
import { PrintType, usePrintTags } from '@/hooks/usePrintTags';
import { useLineStore } from '@/store/modules/line';
import { useUserStore } from '@/store/modules/user';
import { useMessage } from 'naive-ui';
import { computed, ref } from 'vue';
import { VxeGridProps } from 'vxe-table';

const props = withDefaults(defineProps<{ is_over_print?: boolean }>(), {
  is_over_print: false,
});
const modalVisible = ref(false)
const handleClick = () => {
  modalVisible.value = true
  getTableData()
  getMaxPackQty()
}
const gridConfig: VxeGridProps<PrintItem> = {
  columns: [
    { field: "line_code", title: "线别" },
    { field: "order_no", title: "工单号" },
    { field: "serial_number", title: "条码" },
    { field: "quantity", title: "数量" },
    { field: "lot_code", title: "批次号" },
    { field: "creation_date", title: "打印时间" },
  ],
  radioConfig: {
    highlight: true,
  },
  border: true,
  height: '400px'
};
const orderPrintInfo = ref<OrderPrintInfo>()
const tableData = ref<PrintItem[]>([])
const lineStore = useLineStore()
const getTableData = async () => {
  const res = await fetchPrintRecord(lineStore.lineTaskInfo.order_no)
  orderPrintInfo.value = res.data
  tableData.value = res.data.sn_list
}
const userStore = useUserStore()
const messgae = useMessage()
const { printTags, printerList } = usePrintTags(PrintType.COMMON)
const form = ref({
  build_qty: 1,
  printerName: '',
  max_pack_qty: 1
})
const rules = {
  build_qty: [{ required: true, message: '请输入' }],
  printerName: [{ required: true, messgae: '请选择' }]
}
const handlePrint = async () => {
  const res1 = await createSerialNumber({
    workcode: userStore.checker.workcode,
    line_code: lineStore.lineInfo.line_code,
    task_no: lineStore.lineTaskInfo.task_no,
    build_qty: form.value.build_qty,
    is_over_print: props.is_over_print,
    max_pack_qty: form.value.max_pack_qty
  })

  for (const item of res1.data) {
    const res2 = await fetchPrintContent(item.serial_number);
    printTags({
      printerName: res2.data.printer,
      rawCode: res2.data.zpl,
    });
  }
  getMaxPackQty()
}

const getMaxPackQty = async () => {
  const res = await fetchMaxPackQty(lineStore.lineTaskInfo.product_code)
  form.value.max_pack_qty = res.data.max_pack_qty
}
const title = computed(() => {
  return props.is_over_print ? '超单打印' : '打印标签'
})
</script>

<template>
  <n-button class="w-full h-16 bg-[#607d8b] text-white" size="large" @click="handleClick">{{ title }}</n-button>

  <n-modal v-model:show="modalVisible" preset="card" :title="title" class="w-[800px]">
    <div class="w-full h-full">
      <n-form ref="formRef" :model="form" label-placement="left" label-align="right" label-width="80px" :rules="rules">
        <n-form-item :span="22" label="打印数量" path="build_qty">
          <n-input-number v-model:value="form.build_qty" button-placement="both" :min="1" :max="5" class="w-full"
            siez="large" />
        </n-form-item>
        <n-form-item :span="22" label="最大包装数" path="max_pack_qty">
          <n-input-number v-model:value="form.max_pack_qty" button-placement="both" :min="1" class="w-full"
            siez="large" />
        </n-form-item>
        <!-- <n-form-item :span="22" label="打印机" path="printerName">
          <n-select v-model:value="form.printerName" :options="printerList" to="body" label-field="name"
            value-field="name"></n-select>
        </n-form-item> -->
      </n-form>
      <vxe-grid v-bind="gridConfig" :data="tableData">
      </vxe-grid>
    </div>
    <template #footer>
      <n-flex justify="space-between" class="mt-4">
        <n-text strong>打印个数：{{ orderPrintInfo?.sum_sn_count }}</n-text>
        <n-text strong>打印数量累计：{{ orderPrintInfo?.sum_sn_qty }}</n-text>
        <n-text strong>剩余数量：{{ orderPrintInfo?.unproduced_qty }}</n-text>
      </n-flex>
      <n-flex justify="end">
        <n-button @click="modalVisible = false">取消</n-button>
        <n-button type="info" @click="handlePrint">确定</n-button>
      </n-flex>
    </template>
  </n-modal>
</template>