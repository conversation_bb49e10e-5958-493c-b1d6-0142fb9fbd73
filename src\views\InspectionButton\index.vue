<script setup lang="ts">
import { fetchInspectionRecord, scanInspection, submitInspection, removeSerialNumber } from '@/api';
import { ScannedInspectionItem, UnScannedInspectionItem } from '@/api/interface';
import { useLineStore } from '@/store/modules/line';
import { useUserStore } from '@/store/modules/user';
import { useDialog, useMessage } from 'naive-ui';
import { ref, computed } from 'vue';
import { VxeGridProps } from 'vxe-table';

const modalVisible = ref(false)
const handleClick = () => {
  modalVisible.value = true
  const showDelBtn = userStore.checker.role_code === "ims_line_leader"
  const item = leftGridConfig.columns?.find(item => item.field === 'action')
  if (item) {
    item.visible = showDelBtn
  }
  getTableData()
}
const serial_number = ref('')
const userStore = useUserStore()
const message = useMessage()
const handleScan = async () => {
  try {
    const res = await scanInspection({ workcode: userStore.checker.workcode, line_code: lineStore.lineInfo.line_code, serial_number: serial_number.value })
    message.success(res.msg)
    getTableData()
  } finally {
    serial_number.value = ""
  }
}
const lineStore = useLineStore()
const leftTableData = ref<ScannedInspectionItem[]>([])
const rightTableData = ref<UnScannedInspectionItem[]>([])
const getTableData = async () => {
  const res = await fetchInspectionRecord(lineStore.lineInfo.line_code)
  leftTableData.value = res.data.scanned;
  rightTableData.value = res.data.unscanned;
}

const leftTotalQuantity = computed(() => 
  leftTableData.value.reduce((sum, item) => sum + (item.quantity || 0), 0)
);
const leftSerialNumberCount = computed(() => leftTableData.value.length);

const rightTotalQuantity = computed(() =>
  rightTableData.value.reduce((sum, item) => sum + (item.quantity || 0), 0)
);
const rightSerialNumberCount = computed(() => rightTableData.value.length);

const leftGridConfig: VxeGridProps<ScannedInspectionItem> = {
  columns: [
    { 
      type: 'seq',  // 新增序号列
      title: '序号',
      width: 60 
    },
    { field: "order_no", title: "工单号" },
    { field: "product_code", title: "产品编码" },
    { field: "serial_number", title: "条码" },
    { field: "quantity", title: "数量" },
    { field: "lot_code", title: "批次号" },
    { field: "creation_date", title: "扫描时间" },
    { field: "action", title: "操作", slots: { default: "action" } },
  ],
  radioConfig: {
    highlight: true,
  },
  border: true,
  height: '100%'
};

const rightGridConfig: VxeGridProps<UnScannedInspectionItem> = {
  columns: [
    { 
      type: 'seq',  // 新增序号列
      title: '序号',
      width: 60 
    },
    { field: "serial_number", title: "条码" },
    { field: "quantity", title: "数量" },
  ],
  radioConfig: {
    highlight: true,
  },
  border: true,
  height: '100%'
};

const dialog = useDialog()
const submit = () => {
  dialog.warning({
    title: '提示',
    content: '是否确认提交？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const res = await submitInspection({ workcode: userStore.checker.workcode, line_code: lineStore.lineInfo.line_code })
      message.success(res.msg)
      modalVisible.value = false
    }
  })
}
const del = (row: ScannedInspectionItem) => {
  dialog.warning({
    title: '提示',
    content: '是否确认删除？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const res = await removeSerialNumber({ workcode: userStore.checker.workcode, line_code: lineStore.lineInfo.line_code, serial_number: row.serial_number })
      message.success(res.msg)
      getTableData()
    }
  })
}
</script>

<template>
  <n-button class="w-full h-16 bg-[#3ba2f4] text-white" size="large" @click="handleClick">报检</n-button>

  <n-modal v-model:show="modalVisible" preset="card" title="报检" class="w-screen h-screen">
    <div class="w-full h-full">
      <div class="flex mb-4 justify-center items-center">
        <div class="w-16 text-center">条码</div>
        <n-input placeholder="请扫描" @keyup.enter="handleScan" v-model:value="serial_number" size="large"></n-input>
        <n-button class="mx-4 w-72" type="info" size="large" @click="submit">提交</n-button>
      </div>
      <div class="w-full h-[calc(100%-70px)] flex gap-4">
        <!-- 左侧已扫描表格 -->
         <div class="w-[70%] min-w-[300px] flex flex-col">
          <div class="flex-1 overflow-auto">
            <vxe-grid v-bind="leftGridConfig" :data="leftTableData">
              <template #action="{ row }">
                <n-button type="info" size="small" @click="del(row)">
                  删除
                </n-button>
              </template>
            </vxe-grid>
          </div>
          <div class="stat-row p-2 mt-2 rounded shrink-0 flex justify-between">
            <n-text class="mr-4">数量统计：{{ leftTotalQuantity }}</n-text>
            <n-text>条码个数：{{ leftSerialNumberCount }}</n-text>
          </div>
        </div>

        <!-- 右侧未扫描表格 -->
        <div class="w-[30%] min-w-[200px] flex flex-col">
          <div class="flex-1 overflow-auto">
            <vxe-grid v-bind="rightGridConfig" :data="rightTableData" />
          </div>
          <div class="stat-row p-2 mt-2 rounded shrink-0 flex justify-between">
            <n-text class="mr-4">数量统计：{{ rightTotalQuantity }}</n-text>
            <n-text>条码个数：{{ rightSerialNumberCount }}</n-text>
          </div>
        </div>
      </div>
    </div>
  </n-modal>
</template>