<script setup lang="ts">
import { fetchSerialNumberInfo, scrapSerialNumber } from '@/api';
import { PrintItem } from '@/api/interface';
import { useLineStore } from '@/store/modules/line';
import { useUserStore } from '@/store/modules/user';
import { useMessage } from 'naive-ui';
import { ref, useTemplateRef } from 'vue';
import { VxeGridInstance, VxeGridProps } from 'vxe-table';

const modalVisible = ref(false)
const handleClick = () => {
  modalVisible.value = true
}
const tableData = ref<PrintItem[]>([])
const serial_number = ref('')
const gridConfig: VxeGridProps<PrintItem> = {
  columns: [
    { type: "radio", width: 40 },
    { field: "line_code", title: "线别" },
    { field: "order_no", title: "工单号" },
    { field: "serial_number", title: "条码" },
    { field: "quantity", title: "数量" },
    { field: "lot_code", title: "批次号" },
    { field: "creation_date", title: "打印时间" },
  ],
  radioConfig: {
    highlight: true,
  },
  border: true,
  height: '400px'
};
const selected = ref<PrintItem | null>(null);
const gridRef = useTemplateRef<VxeGridInstance>('gridRef')
const handleSelect = ({ row }) => {
  selected.value = row;
  gridRef.value?.setRadioRow(row);
};
const handleScan = async () => {
  try {
    const res = await fetchSerialNumberInfo(serial_number.value)
    tableData.value = res.data
  } finally {
    serial_number.value = ''
  }
}
const userStore = useUserStore()
const lineStore = useLineStore()
const message = useMessage()
const handleSubmit = async () => {
  if (!selected.value) {
    message.error('请选择')
    return
  }
  const res2 = await scrapSerialNumber({ workcode: userStore.checker.workcode, line_code: lineStore.lineInfo.line_code, serial_number: selected.value.serial_number });
  message.success(res2.msg)
}
</script>

<template>
  <n-button class="w-full h-16 bg-[#3f51b5] text-white" size="large" @click="handleClick">标签报废</n-button>

  <n-modal v-model:show="modalVisible" preset="card" title="标签报废" class="w-[800px]">
    <div class="w-full h-full">
      <div class="flex mb-4 justify-center items-center">
        <div class="w-16 text-center">条码</div>
        <n-input placeholder="请扫描" @keyup.enter="handleScan" v-model:value="serial_number"></n-input>
      </div>
      <vxe-grid v-bind="gridConfig" :data="tableData" @cell-click="handleSelect" @radio-change="handleSelect"
        ref="gridRef">
      </vxe-grid>
    </div>
    <template #footer>
      <n-flex justify="end">
        <n-button @click="modalVisible = false">取消</n-button>
        <n-button type="info" @click="handleSubmit">确定</n-button>
      </n-flex>
    </template>
  </n-modal>
</template>