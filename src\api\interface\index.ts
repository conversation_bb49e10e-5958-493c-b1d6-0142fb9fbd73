import { RoleCodeEnum } from "@/types";

export interface LineInfo {
  line_code: string; //线别编码
  line_name: string; // 线别名
}

export interface UserInfo {
  role_code: string;
  role_name: string;
  username: string;
  workcode: string;
}

export interface OfflineRolePayload {
  line_code: string;
  role_code: RoleCodeEnum;
}

export interface UplineRolePayload extends OfflineRolePayload {
  workcode: string;
}

export interface CheckRolePermissionPayload {
  workcode: string;
  role_code: RoleCodeEnum;
}

export interface CheckRolePermissionResult {
  has_permission: boolean;
}

export interface ChangeLineTaskStatusPayload {
  line_code: string;
  workcode: string;
  task_no: string;
}

export interface LoadRecord {
  material_code: string;
  serial_number: string;
  balance_qty: number;
}

export interface OrderItem {
  order_no: string;
  material_code: string;
  material_name: string;
  material_unit: string;
  demand_qty: number;
  load_qty: number;
  load_records: LoadRecord[];
}

export interface UpdateMaterialStatusPayload {
  workcode: string;
  line_code: string;
  task_no: string;
  serial_number: string;
}

export interface ScannedInspectionItem {
  order_no: string;
  serial_number: string;
  quantity: number;
  lot_code: string;
  creation_date: string;
}

export interface UnScannedInspectionItem {
  serial_number: string;
  quantity: number;
}

export interface InspectionInfo {
  scanned: ScannedInspectionItem[];
  unscanned: UnScannedInspectionItem[];
}

export interface ScanInspectionPayload {
  workcode: string;
  line_code: string;
  serial_number: string;
}

export interface SubmitInspectionPayload {
  workcode: string;
  line_code: string;
}

export interface PrintItem {
  creation_date: string;
  line_code: string;
  task_no: string;
  order_no: string;
  serial_number: string;
  quantity: number;
  lot_code: string;
};

export interface OrderPrintInfo {
  sum_sn_count: number;
  sum_sn_qty: number;
  unproduced_qty: number;
  sn_list: PrintItem[];
}

export interface SerialNumberTemplateInfo {
  serial_number: string,
  template_name: string;
  package_type_name: string;
};

export interface createSerialNumberPayload {
  workcode: string
  line_code: string
  task_no: string
  build_qty: number
  is_over_print: boolean
  max_pack_qty: number
}

export interface BartenderPrintPayload {
  printerName: string // 打印机名称
  codes?: string[] // 打印数据
  marks?: string // 打印模板
}

export interface PrintResult {
  code: number
  msg: string
}

export interface CommonPrintPayload {
  printerName: string // 打印机名称
  rawCode?: string // 打印数据
}

export interface PrinterItem {
  name: string
  portName: string
  driverName: string
  printProcessor: string
  datatype: string
  status: unknown[]
  statusNumber: number
  attributes: string[]
  priority: number
  defaultPriority: number
  averagePPM: number
}

export interface PrintContent {
  printer: string
  zpl: string
}

export interface ScrapSerialNumberPayload {
  workcode: string
  line_code: string
  serial_number: string
}