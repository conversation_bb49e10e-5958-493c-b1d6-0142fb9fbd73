<script setup lang="ts">
import { useLineStore } from "@/store/modules/line";

const lineStore = useLineStore();
const taskStatusMap = {
  not_started: '未开始',
  started: '进行中',
  finished: '已完成'
}
</script>

<template>
  <div class="main-box flex-[3] h-[300px]">
    <div class="main-title flex justify-between">
      <div>
        <i class="uil uil-sync text-[#4caf50]"></i>
        <span class="mr-[10px]">生产状态</span>
        <span class="text-[#8ebb86]">{{ taskStatusMap[lineStore.lineTaskInfo.task_status] }}</span>
      </div>
      <!-- <div>
        <span class="font-bold">当前工单：</span>
        <span class="text-[#ff5252] h-[20px]">{{ lineStore.prodInfo.process_name }}</span>
      </div> -->
    </div>
    <n-grid x-gap="12" :cols="3" class="h-full p-8">
      <!-- <n-gi>
        <div class="font-bold text-center">任务码数量</div>
        <div class="text-[#2196f2] h-[20px] text-center">{{ lineStore.lineTaskInfo.task_qty }}</div>
      </n-gi>
      <n-gi>
        <div class="font-bold text-center">已打印数量</div>
        <div class="text-[#4caf50] h-[20px] text-center">{{ lineStore.lineTaskInfo.produced_qty }}</div>
      </n-gi> -->
      <n-gi>
        <div class="font-bold text-center">报检数</div>
        <div class="text-[#ff5252] h-[20px] text-center">{{ lineStore.lineTaskInfo.inspected_qty }}</div>
      </n-gi>
      <n-gi>
        <div class="font-bold text-center">入库数</div>
        <div class="text-[#ff9800] h-[20px] text-center">{{ lineStore.lineTaskInfo.storaged_qty }}</div>
      </n-gi>
      <n-gi>
        <div class="font-bold text-center">报工数</div>
        <div class="text-[#9c27b0] h-[20px] text-center">{{ lineStore.lineTaskInfo.reported_qty }}</div>
      </n-gi>
    </n-grid>
  </div>
</template>
