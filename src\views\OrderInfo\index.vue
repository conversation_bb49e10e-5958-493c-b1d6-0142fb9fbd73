<script setup lang="ts">
import { useLineStore } from "@/store/modules/line";
import { storeToRefs } from "pinia";

const lineStore = useLineStore();
const { lineTaskInfo } = storeToRefs(lineStore)
</script>

<template>
  <div class="main-box flex-[3]">
    <div class="main-title">
      <i class="uil uil-receipt text-[#E68D08]"></i>
      <span>工单信息</span>
    </div>
    <div class="px-8 py-4 w-full">
      <n-descriptions label-placement="left" size="large" :column="2">
        <n-descriptions-item label="客户编码">
          {{ lineTaskInfo.customer_code ?? '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="工单号">
          {{ lineTaskInfo.order_no ?? '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="产品编码">
          {{ lineTaskInfo.product_code ?? '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="产品名称">
          {{ lineTaskInfo.product_name ?? '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="工单数量">
          {{ lineTaskInfo.order_qty ?? '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="实际开工日期">
          {{ lineTaskInfo.actual_start_time ?? '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="良品数量">
          {{ lineTaskInfo.goodness_qty ?? '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="模具别名">
          {{ lineTaskInfo.mold_alias ?? '-' }}
        </n-descriptions-item>
      </n-descriptions>
    </div>
  </div>
</template>

<style scoped>
.n-descriptions {
  font-size: 16px;
  color: theme('colors.text-color');
}
</style>
