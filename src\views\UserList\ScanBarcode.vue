<script setup lang="ts">
import { ref, useTemplateRef } from "vue";
import { checkRolePermission } from "@/api";
import { RoleCodeEnum } from "@/types";
import { useMessage } from "naive-ui";
import { useUserStore } from "@/store/modules/user";

const scanData = ref<string>("");
const scanRef = useTemplateRef<HTMLInputElement>("scanRef");
const props = withDefaults(
  defineProps<{ roleField: string; check?: boolean }>(),
  { roleField: "", check: false }
);
const emits = defineEmits(["success"]);
const modalVisible = defineModel<boolean>("visible");
const message = useMessage();
const userStore = useUserStore()
const scan = async () => {
  try {
    if (scanData.value.includes("workcode") && scanData.value.includes("key")) {
      const { workcode } = JSON.parse(scanData.value);
      if (props.check) {
        const res = await checkRolePermission({
          workcode,
          role_code: props.roleField as RoleCodeEnum,
        });
        if (!res.data.has_permission) {
          message.error("没有权限");
          return;
        }
        userStore.setChecker(res.data)
      }
      emits("success", workcode);
      modalVisible.value = false;
    }
  } finally {
    scanData.value = "";
  }
};
const open = () => {
  scanRef.value?.focus();
};
</script>

<template>
  <n-modal v-model:show="modalVisible" preset="card" title="请扫描" :on-after-enter="open" class="w-[300px]">
    <n-input v-model:value="scanData" placeholder="扫描二维码" ref="scanRef" type="password" @keyup.enter="scan"
      size="large" />
  </n-modal>
</template>
