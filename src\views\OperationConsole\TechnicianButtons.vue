<script setup lang="ts">
import SelectLinePlan from "@/views/SelectLinePlan/index.vue";
import StartLineTaskButton from "@/views/StartLineTaskButton/index.vue"
import PauseLineTaskButton from "@/views/PauseLineTaskButton/index.vue"
import FinishLineTaskButton from "@/views/FinishLineTaskButton/index.vue"
import UpperAndLowerMolds from "@/views/UpperAndLowerMolds/index.vue"

</script>

<template>
	<n-grid :x-gap="12" :y-gap="8" :cols="3">
		<n-grid-item>
			<StartLineTaskButton />
		</n-grid-item>
		<n-grid-item>
			<PauseLineTaskButton />
		</n-grid-item>
		<n-grid-item>
			<FinishLineTaskButton />
		</n-grid-item>
		<n-grid-item>
			<SelectLinePlan />
		</n-grid-item>
		<n-grid-item>
			<UpperAndLowerMolds />
		</n-grid-item>
	</n-grid>
</template>
