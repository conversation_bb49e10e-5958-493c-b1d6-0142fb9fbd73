{"name": "zw_driver_tool", "private": true, "main": "dist-electron/main/index.js", "description": "zw_driver_tool", "author": "<PERSON>", "version": "1.0.2", "scripts": {"serve": "vite", "build-test": "vite build --mode test && electron-builder --win --config electron-builder.test.json", "build": "vite build && electron-builder --win", "preview": "vite preview"}, "dependencies": {"@thiagoelg/node-printer": "^0.6.2", "axios": "^1.9.0", "body-parser": "^2.2.0", "electron-updater": "^6.6.2", "express": "^5.1.0", "lucide-vue-next": "^0.510.0", "naive-ui": "^2.41.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "vue": "^3.5.13", "vxe-table": "4.6.17"}, "devDependencies": {"@types/express": "^5.0.2", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "electron": "^22.3.27", "electron-builder": "^24.13.3", "postcss": "^8.5.3", "sass": "^1.88.0", "tailwindcss": "^3.4.17", "typescript": "5.6.3", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "^5.4.19", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vue-tsc": "^2.2.10"}}