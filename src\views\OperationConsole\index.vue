<script setup lang="ts">
import { onMounted, ref } from "vue";
import OperatorButtons from "@/views/OperationConsole/OperatorButtons.vue";
import HeadmanButtons from "@/views/OperationConsole/HeadmanButtons.vue";
import TechnicianButtons from "@/views/OperationConsole/TechnicianButtons.vue";
import MaterialButtons from "@/views/OperationConsole/MaterialButtons.vue";
import { useUserStore } from "@/store/modules/user";
import { RoleCodeEnum, RoleCodeToChinese } from "@/types";
import ScanBarcode from "@/views/UserList/ScanBarcode.vue";
import { useLineStore } from "@/store/modules/line";

const roleType = ref<string>("");
const roleName = ref<string>("");
const checkStatus = ref<boolean>(false);
const scanBarcodeVisible = ref(false);
const handleClickRoleBtn = async (type: string, name: string) => {
  roleType.value = type;
  roleName.value = name;
  scanBarcodeVisible.value = true
};
const success = async () => {
  checkStatus.value = true
};
const back = () => {
  checkStatus.value = false;
};
const userStore = useUserStore();
const lineStore = useLineStore()
</script>

<template>
  <div class="main-box flex-[2] ">
    <div class="main-title">
      <template v-if="!checkStatus">
        <i class="uil uil-fidget-spinner text-[#f44336]"></i>
        <span>操作台</span>
      </template>
      <template v-else>
        <div class="flex justify-between w-full">
          <button @click="back"
            class="flex items-center gap-2 px-4 py-2 bg-[#3ba2f4] text-white rounded-lg hover:bg-[#1359a8] transition-colors">
            返回操作台
          </button>
          <span>{{ userStore.checker.workcode }}/{{ roleName }}</span>
        </div>
      </template>
    </div>
    <div class="p-[10px]">
      <n-grid :x-gap="12" :y-gap="8" :cols="2" v-if="!checkStatus">
        <n-grid-item>
          <n-button class="w-full h-16 bg-[#1867c0] text-white text-4 rounded-lg" size="large"
            :disabled="!lineStore.lineInfo.line_name || !userStore.ims_operator.workcode || !lineStore.lineTaskInfo.task_no"
            @click="handleClickRoleBtn(RoleCodeEnum.OPERATOR, RoleCodeToChinese[RoleCodeEnum.OPERATOR])">作业员</n-button>
        </n-grid-item>
        <n-grid-item>
          <n-button class="w-full h-16 bg-[#706bff] text-white text-4 rounded-lg" size="large"
            :disabled="!lineStore.lineInfo.line_name"
            @click="handleClickRoleBtn(RoleCodeEnum.LINE_LEADER, RoleCodeToChinese[RoleCodeEnum.LINE_LEADER])">生产组长</n-button>
        </n-grid-item>
        <n-grid-item>
          <n-button class="w-full h-16 bg-[#8754df] text-white text-4 rounded-lg" size="large"
            :disabled="!lineStore.lineInfo.line_name"
            @click="handleClickRoleBtn(RoleCodeEnum.TECHNICIAN, RoleCodeToChinese[RoleCodeEnum.TECHNICIAN])">技术员</n-button>
        </n-grid-item>
        <n-grid-item>
          <n-button class="w-full h-16 bg-[#60609c] text-white text-4 rounded-lg" size="large"
            :disabled="!lineStore.lineInfo.line_name || !lineStore.lineTaskInfo.task_no"
            @click="handleClickRoleBtn(RoleCodeEnum.MATERIAL_LOADER, RoleCodeToChinese[RoleCodeEnum.MATERIAL_LOADER])">料房</n-button>
        </n-grid-item>
      </n-grid>
      <!--作业员按钮-->
      <OperatorButtons v-if="checkStatus && roleType === RoleCodeEnum.OPERATOR" />
      <!--生产组长按钮-->
      <HeadmanButtons v-else-if="checkStatus && roleType === RoleCodeEnum.LINE_LEADER" />
      <!--技术员按钮-->
      <TechnicianButtons v-else-if="checkStatus && roleType === RoleCodeEnum.TECHNICIAN" />
      <!--料房按钮-->
      <MaterialButtons v-else-if="checkStatus && roleType === RoleCodeEnum.MATERIAL_LOADER" />
    </div>
  </div>
  <ScanBarcode v-model:visible="scanBarcodeVisible" @success="success" :role-field="roleType" :check="true" />
</template>
