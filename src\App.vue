<script setup lang="ts">
import Home from "@/views/home.vue"
import { useLineStore } from "./store/modules/line";
import { onUnmounted, ref } from "vue";

const lineStore = useLineStore()
const timer = ref<number | null>(null)
const setIntervalImmediately = (func: Function, interval: number) => {
  func()
  return setInterval(func, interval)
}
if (window.ipcRenderer) {
  window.ipcRenderer.on("update-available", () => {
    alert("有新版本，是否更新？");
    window.ipcRenderer.send("download-update");
  });
  window.ipcRenderer.on("update-not-available", () => {
    alert("当前已经是最新版")
  });
  window.ipcRenderer.on("update-error", () => {
    alert("检查更新失败")
  });
  window.addEventListener("keyup", (e) => {
    if (e.key === 'F12') {
      window.ipcRenderer.send('open-dev-tools')
    }
    if (e.key === 'F5') {
      window.location.reload()
    }
  })
  window.ipcRenderer.on("console-log", (_, ...args) => {
    console.log('主进程:', ...args)
  })
  window.ipcRenderer.send('get-mac')
  window.ipcRenderer.on("set-mac", (_, mac) => {
    lineStore.setMac(mac)
    timer.value = setIntervalImmediately(lineStore.getLineInfo, 10 * 60 * 1000)
  })
} else {
  timer.value = setIntervalImmediately(lineStore.getLineInfo, 10 * 60 * 1000)
}

onUnmounted(() => {
  timer.value && clearInterval(timer.value);
});

const handelDebug = () => {
  window.ipcRenderer && window.ipcRenderer.send('open-dev-tools')
}
const handleMini = () => {
  window.ipcRenderer && window.ipcRenderer.send('mini')
}
</script>

<template>
  <div class="relative w-full h-full">
    <n-dialog-provider>
      <n-message-provider>
        <Home />
      </n-message-provider>
    </n-dialog-provider>

    <n-flex class="absolute bottom-8 left-4">
      <n-button type="text" @click="handelDebug">DEBUG</n-button>
      <n-button type="text" @click="handleMini">最小化</n-button>
    </n-flex>
  </div>
</template>

<style scoped></style>
