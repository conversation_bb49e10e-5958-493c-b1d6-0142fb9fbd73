import { defineStore } from "pinia";

export const useUserStore = defineStore("user", {
  state: () => ({
    ims_operator: {}, // 作业员
    ims_technician: {}, // 技术员
    ims_inspector: {}, // 巡检员
    checker: {}, // 当前校验身份权限人员
    ims_material_loader: {}, // 料房
  }),
  actions: {
    setOperator(info) {
      this.ims_operator = info;
    },
    setTechnician(info) {
      this.ims_technician = info;
    },
    setInspector(info) {
      this.ims_inspector = info;
    },
    set<PERSON><PERSON><PERSON>(info) {
      this.checker = info;
    },
    setMaterialLoader(info) {
      this.ims_material_loader = info
    }
  },
});
