<script setup lang="ts">
import { finishLineTask } from '@/api';
import { useLineStore } from '@/store/modules/line';
import { useUserStore } from '@/store/modules/user';
import { useMessage } from 'naive-ui';

const lineStore = useLineStore()
const userStore = useUserStore()
const message = useMessage()
const handleClick = async () => {
  const res = await finishLineTask({ line_code: lineStore.lineInfo.line_code, task_no: lineStore.lineTaskInfo.task_no, workcode: userStore.checker.workcode })
  message.success(res.msg)
  lineStore.getLineTaskInfo()
}
</script>

<template>
  <n-button class="w-full h-16 bg-[#156aa8] text-white" size="large" @click="handleClick">工单完工</n-button>
</template>