import { defineStore } from "pinia";
import { fetchLineInfo, fetchLineTaskInfo } from "@/api";

export const useLineStore = defineStore("line", {
  state: () => ({
    lineInfo: {} as any, // 线别数据
    lineTaskInfo: {} as any, // 线别工单数据
    planOrderInfo: {},
    prodInfo: {},
    mac: "",
  }),
  actions: {
    setMac(value: string) {
      this.mac = value;
    },
    setLineTaskInfo(data: any) {
      this.lineTaskInfo = data;
    },
    // 获取线别
    async getLineInfo() {
      const res = await fetchLineInfo(
        localStorage.getItem("mac") || this.mac
      );
      this.lineInfo = res.data;
      this.getLineTaskInfo();
    },
    async getLineTaskInfo() {
      try {
        const res = await fetchLineTaskInfo(this.lineInfo?.line_code || "");
        this.lineTaskInfo = res.data;
      } catch (e) {
        this.lineTaskInfo = {};
      }
    },
  },
});
