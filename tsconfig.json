{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "typeRoots": ["./node_modules/@types", "./src/types"], "baseUrl": "./", "paths": {"@": ["src"], "@/*": ["src/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}], "exclude": ["node_modules", "dist", "dist-electron"]}