image: ************:9092/electron-builder:wine-nvm

stages:
  - build
  - deploy

variables:
  NPM_CONFIG_REGISTRY: https://registry.npmmirror.com
  ELECTRON_MIRROR: https://npmmirror.com/mirrors/electron/
  ELECTRON_BUILDER_BINARIES_MIRROR: https://npmmirror.com/mirrors/electron-builder-binaries/
  SERVER_IP: "***********"
  ELECTRON_PATH: "/home/<USER>/electron"
  WEBVIEW_PATH: '/home/<USER>/web'
  FEED_URL: "http://***********:7860"

before_script:
  - 'command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )'
  - apt-get update -y
  - apt-get install -y jq libcups2-dev build-essential python3-dev
  - eval $(ssh-agent -s)
  - chmod 400 "$SSH_PRIVATE_KEY"
  - ssh-add "$SSH_PRIVATE_KEY"
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - cp "$SSH_KNOWN_HOSTS" ~/.ssh/known_hosts
  - chmod 644 ~/.ssh/known_hosts
  - export NVM_DIR="$HOME/.nvm"
  - '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"'
  - nvm use 18

cache:
  paths:
    - node_modules
    - .cache/electron
    - .cache/electron-builder
    - .npm-cache
    - /root/.npm/_prebuilds

build:
  stage: build
  script:
    - npm install yarn -g
    - yarn
    - yarn build
  artifacts:
    paths:
      - release/*.exe
      - release/latest.yml
      - dist/*
    expire_in: 1 day
  tags:
    - sonar_docker
  only:
    - main

build_test:
  stage: build
  script:
    - npm install yarn -g
    - yarn
    - yarn build-test
  artifacts:
    paths:
      - release/*.exe
      - release/latest.yml
      - dist/*
    expire_in: 1 day
  tags:
    - sonar_docker
  only:
    - test

publish_to_releases:
  stage: deploy
  dependencies:
    - build
  script:
    - |
      #!/bin/bash      
      RELEASE_TAG="v$(node -p "require('./package.json').version")"
      PROJECT_NAME="$(node -p "require('./package.json').name")"
      EXISTING_TAG=$(curl -sX GET --header "PRIVATE-TOKEN:${API_TOKEN}" "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases" | jq -r 'if length > 0 then .[0].tag_name else "" end')
      
      ssh ${SERVER_IP} "mkdir -p ${WEBVIEW_PATH}/${PROJECT_NAME}"
      scp -r dist/* ${SERVER_IP}:${WEBVIEW_PATH}/${PROJECT_NAME}/

      if [ -z "$EXISTING_TAG" ] || [ "$RELEASE_TAG" != "$EXISTING_TAG" ]; then
        curl -sX POST \
            --form "tag_name=${RELEASE_TAG}" \
            --form "name=${RELEASE_TAG}" \
            --form "ref=main" \
            --header "PRIVATE-TOKEN:${API_TOKEN}" \
            "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases"
        
        APP_NAME="${PROJECT_NAME}_${RELEASE_TAG}_Setup.exe"
        APP_URL="${FEED_URL}/${PROJECT_NAME}/${APP_NAME}"

        ssh ${SERVER_IP} "mkdir -p ${ELECTRON_PATH}/${PROJECT_NAME}"
        scp release/latest.yml ${SERVER_IP}:${ELECTRON_PATH}/${PROJECT_NAME}/
        scp release/${APP_NAME} ${SERVER_IP}:${ELECTRON_PATH}/${PROJECT_NAME}/

        curl --location "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases/${RELEASE_TAG}/assets/links" \
             --header "PRIVATE-TOKEN:${API_TOKEN}" \
             --form "name=${APP_NAME}" \
             --form "url=${APP_URL}"
      else
        echo "当前版本与最新tag一致，不发布"
      fi
  tags:
    - sonar_docker
  only:
    - main

publish_to_releases_test:
  stage: deploy
  dependencies:
    - build_test
  script:
    - |
      #!/bin/bash
      RELEASE_TAG="v$(node -p "require('./package.json').version")-test"
      PROJECT_NAME="$(node -p "require('./package.json').name")"
      EXISTING_TAG=$(curl -sX GET --header "PRIVATE-TOKEN:${API_TOKEN}" "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases" | jq -r 'if length > 0 then .[0].tag_name else "" end')
      
      ssh ${SERVER_IP} "mkdir -p ${WEBVIEW_PATH}/${PROJECT_NAME}_test"
      scp -r dist/* ${SERVER_IP}:${WEBVIEW_PATH}/${PROJECT_NAME}_test/

      if [ -z "$EXISTING_TAG" ] || [ "$RELEASE_TAG" != "$EXISTING_TAG" ]; then
        curl -sX POST \
            --form "tag_name=${RELEASE_TAG}" \
            --form "name=${RELEASE_TAG}" \
            --form "ref=test" \
            --header "PRIVATE-TOKEN:${API_TOKEN}" \
            "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases"
        
        APP_NAME="${PROJECT_NAME}_${RELEASE_TAG}_Setup.exe"
        APP_URL="${FEED_URL}/${PROJECT_NAME}_test/${APP_NAME}"

        ssh ${SERVER_IP} "mkdir -p ${ELECTRON_PATH}/${PROJECT_NAME}_test"
        scp release/latest.yml ${SERVER_IP}:${ELECTRON_PATH}/${PROJECT_NAME}_test/
        scp release/${APP_NAME} ${SERVER_IP}:${ELECTRON_PATH}/${PROJECT_NAME}_test/

        curl --location "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases/${RELEASE_TAG}/assets/links" \
             --header "PRIVATE-TOKEN:${API_TOKEN}" \
             --form "name=${APP_NAME}" \
             --form "url=${APP_URL}"
      else
        echo "当前版本与最新tag一致，不发布"
      fi
  tags:
    - sonar_docker
  only:
    - test
