export interface ApiResponse<T> {
  code: number;
  data: T;
  msg: string;
}

/**
 * @description 角色编码
 */
export enum RoleCodeEnum {
  OPERATOR = "ims_operator", // 作业员
  TECHNICIAN = "ims_technician", // 技术员
  MATERIAL_LOADER = "ims_material_loader", // 料房
  INSPECTOR = "ims_inspector", // 巡检
  LINE_LEADER = "ims_line_leader", //生产组长
}

export const RoleCodeToChinese = {
  [RoleCodeEnum.OPERATOR]: "作业员",
  [RoleCodeEnum.TECHNICIAN]: "技术员",
  [RoleCodeEnum.MATERIAL_LOADER]: "料房",
  [RoleCodeEnum.INSPECTOR]: "巡检",
  [RoleCodeEnum.LINE_LEADER]: "生产组长",
};