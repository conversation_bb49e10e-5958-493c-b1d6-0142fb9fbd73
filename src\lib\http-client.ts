import axios, { AxiosInstance, AxiosRequestConfig } from "axios";
import { ApiResponse } from "@/types";
import { computed, ref } from "vue";
import {
  ConfigProviderProps,
  createDiscreteApi,
  darkTheme,
  lightTheme,
} from "naive-ui";

const themeRef = ref<"light" | "dark">("light");
const configProviderPropsRef = computed<ConfigProviderProps>(() => ({
  theme: themeRef.value === "light" ? lightTheme : darkTheme,
}));

const { message } = createDiscreteApi(["message"], {
  configProviderProps: configProviderPropsRef,
});

export class HttpClient {
  private instance: AxiosInstance;
  private baseConfig: AxiosRequestConfig = {
    baseURL:
      window.localStorage.getItem("test_api") || import.meta.env.VITE_BASE_URL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json",
    },
  };

  constructor(config?: AxiosRequestConfig) {
    this.instance = axios.create({
      ...this.baseConfig,
      ...config,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    this.instance.interceptors.request.use(
      (config) => {
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    this.instance.interceptors.response.use(
      (response) => {
        const { data } = response;
        if (data.code && data.code !== 200) {
          message.error(data.msg);
          return Promise.reject(data);
        }
        return data;
      },
      (error) => {
        if (error.response) {
          const { status } = error.response;
          switch (status) {
            case 403:
              break;
          }
        }
        return Promise.reject(error);
      }
    );
  }

  public async get<T>(
    url: string,
    params?: object,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.instance.get(url, { ...config, params });
  }

  public async post<T>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.instance.post(url, data, config);
  }

  public async put<T>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.instance.put(url, data, config);
  }

  public async delete<T>(
    url: string,
    params?: object,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.instance.delete(url, { params, ...config });
  }
}

const request = new HttpClient();
export default request;
