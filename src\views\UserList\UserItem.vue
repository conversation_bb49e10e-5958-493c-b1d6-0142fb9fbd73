<script setup lang="ts">
import { computed, ref } from "vue";
import { useUserStore } from "@/store/modules/user";
import ScanBarcode from "@/views/UserList/ScanBarcode.vue";
import { useLineStore } from "@/store/modules/line";
import { RoleCodeEnum } from "@/types";
import { useMessage } from "naive-ui";
import { offlineRole, uplineRole } from "@/api";

const props = withDefaults(defineProps<{ type: string }>(), {
  type: RoleCodeEnum.OPERATOR,
});
const userStore = useUserStore();
const userOptions = [
  { label: "作业员", value: RoleCodeEnum.OPERATOR },
  { label: "技术员", value: RoleCodeEnum.TECHNICIAN },
  { label: "巡检", value: RoleCodeEnum.INSPECTOR },
  { label: "料房", value: RoleCodeEnum.MATERIAL_LOADER },
];
const userItem = userOptions.find((o) => o.value === props.type);
const currentUserInfo = computed(() => {
  return userItem ? userStore[userItem.value] : {};
});
const isLogin = computed(() => {
  return JSON.stringify(currentUserInfo.value) !== "{}";
});
const lineStore = useLineStore();
const emits = defineEmits(["update"]);
const message = useMessage();
const success = async (workcode: string = '') => {
  if (userItem) {
    if (scanType.value === 1) {
      const res = await uplineRole({
        line_code: lineStore.lineInfo?.line_code || '',
        role_code: userItem.value,
        workcode
      })
      message.success(res.msg);
    } else {
      const res = await offlineRole({
        line_code: lineStore.lineInfo?.line_code || '',
        role_code: userItem.value,
      });
      message.success(res.msg);
      userStore[userItem.value] = {};
    }
  }
  emits("update");
};
const scanBarcodeVisible = ref(false);
// 1：上机；2：下机
const scanType = ref(1);
const scan = (type: number) => {
  scanType.value = type;
  if (type === 1) {
    scanBarcodeVisible.value = true;
  } else {
    success()
  }
};
const photoUrl = computed(() => {
  return currentUserInfo.value?.photo_url || "http://*************/leanmes/Zhaowei/Static/pic/default.jpg";
});
</script>

<template>
  <div class="flex flex-col items-center">
    <img :src="photoUrl" alt="头像" class="w-[100px] h-[100px] mb-[4px] rounded-[20px]" />
    <div class="flex">
      <span v-if="isLogin">{{ currentUserInfo?.username }}/</span>
      <span>{{ userItem?.label }}</span>
    </div>
    <n-button type="warning" v-if="isLogin" @click="scan(2)">下机</n-button>
    <n-button type="info" v-else @click="scan(1)" :disabled="!lineStore.lineInfo.line_name">上机</n-button>
  </div>
  <ScanBarcode v-model:visible="scanBarcodeVisible" @success="success" :role-field="String(userItem?.value)" />
</template>

<style scoped>
.n-button {
  font-size: 14px;
}
</style>
