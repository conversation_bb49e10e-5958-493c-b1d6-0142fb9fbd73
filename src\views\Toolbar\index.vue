<script setup lang="ts">
import { useLineStore } from "@/store/modules/line";
import { LayoutList } from "lucide-vue-next"

const lineStore = useLineStore();
const refresh = () => {
  window.location.reload();
};
</script>

<template>
  <div class="h-[64px] px-[20px] flex justify-between bg-[#1867c0]">
    <div class="flex items-center text-white">
      <LayoutList class="mr-2 w-5 h-5" />
      <span>机台号：{{ lineStore?.lineInfo?.line_name }}</span>
    </div>
    <div class="flex items-center text-white cursor-pointer" @click="refresh">清空缓存</div>
  </div>
</template>

<style scoped>
font-awesome-icon {
  @apply text-white text-2xl mr-2;
}
</style>
