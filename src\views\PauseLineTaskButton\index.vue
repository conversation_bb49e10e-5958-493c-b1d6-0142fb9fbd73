<script setup lang="ts">
import { pauseLineTask } from '@/api';
import { useLineStore } from '@/store/modules/line';
import { useUserStore } from '@/store/modules/user';
import { useMessage } from 'naive-ui';

const lineStore = useLineStore()
const userStore = useUserStore()
const message = useMessage()
const handleClick = async () => {
  const res = await pauseLineTask({ line_code: lineStore.lineInfo.line_code, task_no: lineStore.lineTaskInfo.task_no, workcode: userStore.checker.workcode })
  message.success(res.msg)
  lineStore.getLineTaskInfo()
}
</script>

<template>
  <n-button class="w-full h-16 bg-[#fb8c00] text-white" size="large" @click="handleClick">工单暂停</n-button>
</template>