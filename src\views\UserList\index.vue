<script setup lang="ts">
import UserItem from "@/views/UserList/UserItem.vue";
import { useLineStore } from "@/store/modules/line";
import { useUserStore } from "@/store/modules/user";
import { watch } from "vue";
import { storeToRefs } from "pinia";
import { fetchOnlineRole } from "@/api";
import { RoleCodeEnum } from "@/types";

const lineStore = useLineStore();
const { lineInfo } = storeToRefs(lineStore);
const userStore = useUserStore();
const getOnlineRole = async () => {
  const res = await fetchOnlineRole(lineInfo.value.line_code);
  res.data.forEach((item) => {
    if (item.role_code === RoleCodeEnum.OPERATOR) {
      userStore.setOperator(item);
    }
    if (item.role_code === RoleCodeEnum.TECHNICIAN) {
      userStore.setTechnician(item);
    }
    if (item.role_code === RoleCodeEnum.MATERIAL_LOADER) {
      userStore.setMaterialLoader(item);
    }
    if (item.role_code === RoleCodeEnum.INSPECTOR) {
      userStore.setInspector(item);
    }
  });
};
watch(
  () => lineInfo.value.line_name,
  (value) => {
    if (value) getOnlineRole();
  },
  { immediate: true }
);
</script>

<template>
  <div class="main-box flex-[2] ">
    <div class="main-title">
      <i class="uil uil-users-alt text-[#7F36FF]"></i>
      <span>生产负责</span>
    </div>
    <div class="flex px-[20px] py-[10px] justify-between items-center">
      <!--作业员-->
      <UserItem :type="RoleCodeEnum.OPERATOR" @update="getOnlineRole" />
      <!--技术员-->
      <UserItem :type="RoleCodeEnum.TECHNICIAN" @update="getOnlineRole" />
      <!--巡检-->
      <UserItem :type="RoleCodeEnum.INSPECTOR" @update="getOnlineRole" />
      <!--料房-->
      <UserItem :type="RoleCodeEnum.MATERIAL_LOADER" @update="getOnlineRole" />
    </div>
  </div>
</template>
