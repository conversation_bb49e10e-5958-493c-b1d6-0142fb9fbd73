<script setup lang="ts">
// import { startLineTask } from "@/api";
import { useLineStore } from "@/store/modules/line";
import { useUserStore } from "@/store/modules/user";
import { useMessage } from "naive-ui";
import UpperAndLowerMolds from "./UpperAndLowerMolds.vue";
import { ref } from "vue";
const lineStore = useLineStore();
const userStore = useUserStore();
const UpperAndLowerMoldsRef = ref();
const handleClick = async () => {
    UpperAndLowerMoldsRef.value.show(lineStore.lineInfo.line_code, userStore.checker.workcode);
};
</script>

<template>
<div>
    <n-button
    class="w-full h-16 bg-[#1867c0] text-white"
    size="large"
    @click="handleClick"
    >上下模</n-button
  >
  <UpperAndLowerMolds ref="UpperAndLowerMoldsRef" @change="()=>{
    lineStore.getLineTaskInfo()
  }"/>
</div>
</template>
