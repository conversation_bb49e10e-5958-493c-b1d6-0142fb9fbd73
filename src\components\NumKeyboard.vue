<template>
  <n-grid :cols="3" :x-gap="8" :y-gap="8">
    <n-gi v-for="i in 9" :key="i">
      <n-button type="primary" @click="input(i)">{{ i }}</n-button>
    </n-gi>
    <n-gi>
      <n-button type="primary" @click="del">删除</n-button>
    </n-gi>
    <n-gi>
      <n-button type="primary" @click="input(0)">0</n-button>
    </n-gi>
    <n-gi>
      <n-button type="primary" @click="$emit('reset')">重置</n-button>
    </n-gi>
    <n-gi :span="3" v-if="showButton">
      <n-button type="primary" block @click="$emit('submit')">确定</n-button>
    </n-gi>
  </n-grid>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

const props = defineProps(['value', 'showButton'])
const emit = defineEmits(['input', 'change', 'reset', 'submit'])
const data = ref(props.value)
watch(() => props.value, (val) => {
  data.value = val
})
const input = (n) => {
  if (data.value === undefined) {
    data.value = ""
  }
  data.value = data.value + n
  emit('change', data.value)
}

const del = () => {
  if (data.value.length == 0) return
  data.value = data.value.slice(0, data.value.length - 1)
  emit('change', data.value)
}
</script>

<style scoped>
.n-button {
  width: 100%;
  height: 48px;
  background-color: rgb(33, 150, 243);
  color: white;
}
</style>
