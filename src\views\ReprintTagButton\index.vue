<script setup lang="ts">
import { fetchPrintContent, fetchSerialNumberTemplateInfo } from '@/api';
import { SerialNumberTemplateInfo } from '@/api/interface';
import { PrintType, usePrintTags } from '@/hooks/usePrintTags';
import { useMessage } from 'naive-ui';
import { ref, useTemplateRef } from 'vue';
import { VxeGridInstance, VxeGridProps } from 'vxe-table';

const modalVisible = ref(false)
const handleClick = () => {
  modalVisible.value = true
}
const tableData = ref<SerialNumberTemplateInfo[]>([])
const serial_number = ref('')
const gridConfig: VxeGridProps<SerialNumberTemplateInfo> = {
  columns: [
    { type: "radio", width: 40 },
    { field: "serial_number", title: "SN" },
    { field: "template_name", title: "标签名称" },
    { field: "package_type_name", title: "包装类型" },
  ],
  radioConfig: {
    highlight: true,
  },
  border: true,
  height: '400px'
};
const selected = ref<SerialNumberTemplateInfo | null>(null);
const gridRef = useTemplateRef<VxeGridInstance>('gridRef')
const handleSelect = ({ row }) => {
  selected.value = row;
  gridRef.value?.setRadioRow(row);
};
const handleScan = async () => {
  try {
    const res = await fetchSerialNumberTemplateInfo(serial_number.value)
    tableData.value = res.data
  } finally {
    serial_number.value = ''
  }
}
const message = useMessage()
const { printTags } = usePrintTags(PrintType.COMMON)
const handleSubmit = async () => {
  if (!selected.value) {
    message.error('请选择')
    return
  }
  const res2 = await fetchPrintContent(selected.value.serial_number);
  printTags({
    printerName: res2.data.printer,
    rawCode: String(res2.data.zpl),
  });
}
</script>

<template>
  <n-button class="w-full h-16 bg-[#ff6666] text-white" size="large" @click="handleClick">标签补打</n-button>

  <n-modal v-model:show="modalVisible" preset="card" title="标签补打" class="w-[800px]">
    <div class="w-full h-full">
      <div class="flex mb-4 justify-center items-center">
        <div class="w-16 text-center">条码</div>
        <n-input placeholder="请扫描" @keyup.enter="handleScan" v-model:value="serial_number"></n-input>
      </div>
      <vxe-grid v-bind="gridConfig" :data="tableData" @cell-click="handleSelect" @radio-change="handleSelect"
        ref="gridRef">
      </vxe-grid>
    </div>
    <template #footer>
      <n-flex justify="end">
        <n-button @click="modalVisible = false">取消</n-button>
        <n-button type="info" @click="handleSubmit">确定</n-button>
      </n-flex>
    </template>
  </n-modal>
</template>